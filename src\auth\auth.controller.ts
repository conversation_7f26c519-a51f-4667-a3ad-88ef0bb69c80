import { Body, Controller, Post } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { signUpDto } from "./dto/signUp.dto";
import { IAuth } from "./interfaces/auth.interfaces";

@Controller('auth')
export class AuthController{
    constructor(private readonly authService:AuthService){}

    @Post()
    async signUp(@Body() signUpDto:signUpDto):Promise<IAuth>{
        return this.authService.signUp(signUpDto);
    }
}