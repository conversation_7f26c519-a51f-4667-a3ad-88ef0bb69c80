import { JwtService } from "@nestjs/jwt";
import { UsersService } from "src/users/users.service";
import { signUpDto } from "./dto/signUp.dto";
import { IAuth } from "./interfaces/auth.interfaces";
import { IUsers } from "src/users/interfaces/users.interface";

export class AuthService{
    constructor(private readonly usersService:UsersService,
        private readonly jwtService:JwtService){}

        async signUp(signUpDto:signUpDto):Promise<IAuth>{
            const user: Omit<IUsers,"password">= await this.usersService.createUser(signUpDto);
            const payload={name:user.firstName,_id:user._id};
            const accessToken=await this.jwtService.signAsync(payload);
            return {
                ...user,
                accessToken
            };
        }
}